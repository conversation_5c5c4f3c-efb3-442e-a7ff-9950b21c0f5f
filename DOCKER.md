# Docker 部署指南

本指南将帮助您使用Docker来部署和运行HaoBBS论坛系统。

## 📋 系统要求

- Docker 20.10 或更高版本
- Docker Compose 1.29 或更高版本

## 🚀 快速开始

### 1. 构建并运行应用

```bash
# 克隆项目（如果尚未克隆）
git clone <repository-url>
cd HaoBBS

# 使用Docker Compose构建并启动应用
docker-compose up --build
```

访问 `http://localhost:5002` 即可使用应用。

### 2. 直接使用Docker命令

```bash
# 构建Docker镜像
docker build -t haobbs .

# 运行容器
docker run -d \
  --name haobbs \
  -p 5002:5002 \
  -v $(pwd)/forum.db:/app/forum.db \
  haobbs
```

## 📁 数据持久化

Docker部署通过卷挂载实现数据持久化：

- `forum.db`：SQLite数据库文件
- `static/`：静态资源目录
- `templates/`：模板文件目录

## ⚙️ 环境变量配置

可以通过环境变量自定义应用配置：

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| PORT | 5002 | 应用监听端口 |
| FLASK_ENV | production | Flask运行环境 |
| SECRET_KEY | (随机生成) | Flask密钥 |

在docker-compose.yml中配置：

```yaml
environment:
  - PORT=5002
  - FLASK_ENV=production
  - SECRET_KEY=your-secure-secret-key
```

## 🛠️ 开发模式

### 使用Docker Compose进行开发

```bash
# 启动开发环境（包含CSS监听服务）
docker-compose --profile dev up --build
```

### 热重载开发

通过卷挂载实现代码热重载：
- 修改模板文件会立即生效
- 修改静态资源会立即生效
- Python代码修改需要重启容器

## 🔧 管理命令

### 查看日志

```bash
# 查看应用日志
docker-compose logs haobbs

# 实时查看日志
docker-compose logs -f haobbs
```

### 进入容器

```bash
# 进入运行中的容器
docker exec -it haobbs sh
```

### 停止和删除

```bash
# 停止服务
docker-compose down

# 停止并删除卷（会丢失数据）
docker-compose down -v
```

## 🌐 生产部署

### 构建生产镜像

```bash
# 构建优化的生产镜像
docker build -t haobbs:latest .
```

### 运行生产环境

```bash
# 生产环境运行
docker run -d \
  --name haobbs-prod \
  --restart unless-stopped \
  -p 80:5002 \
  -v /path/to/forum.db:/app/forum.db \
  haobbs:latest
```

## 📊 数据库管理

### 备份数据库

```bash
# 复制容器中的数据库文件
docker cp haobbs:/app/forum.db ./backup/forum_$(date +%Y%m%d).db
```

### 恢复数据库

```bash
# 将备份文件复制到容器
docker cp ./backup/forum_backup.db haobbs:/app/forum.db
```

## 🔒 安全建议

1. **更改默认密钥**：
   ```yaml
   environment:
     - SECRET_KEY=your-very-secure-secret-key-here
   ```

2. **使用非root用户运行容器**：
   Dockerfile中已配置使用非root用户运行应用。

3. **定期更新基础镜像**：
   ```bash
   docker pull python:3.9-alpine
   docker pull node:18-alpine
   ```

## 🐛 故障排除

### 常见问题

1. **端口冲突**：
   ```bash
   # 更改端口映射
   docker run -p 8080:5002 haobbs
   ```

2. **权限问题**：
   ```bash
   # 确保数据库文件可写
   chmod 666 forum.db
   ```

3. **数据库连接失败**：
   ```bash
   # 检查数据库文件权限
   docker exec haobbs ls -la forum.db
   ```

### 查看容器状态

```bash
# 查看运行中的容器
docker ps

# 查看容器详细信息
docker inspect haobbs
```

## 📈 性能优化

### 资源限制

```yaml
# 在docker-compose.yml中添加资源限制
haobbs:
  build: .
  ports:
    - "5002:5002"
  deploy:
    resources:
      limits:
        memory: 512M
        cpus: '0.5'
```

### 多阶段构建优势

- 减小最终镜像大小
- 分离构建和运行时依赖
- 提高构建安全性

## 🔄 更新应用

### 更新到最新版本

```bash
# 拉取最新代码
git pull

# 重新构建并启动
docker-compose up --build -d
```

### 数据库迁移

如果数据库结构有更新，请参考相关迁移文档。
