#!/usr/bin/env python3
"""
HaoBBS 样式测试脚本
用于验证 Tailwind CSS 迁移后的页面渲染是否正常
"""

import requests
import sys
import os
from urllib.parse import urljoin

def test_page_load(base_url, path, expected_status=200):
    """测试页面是否能正常加载"""
    url = urljoin(base_url, path)
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == expected_status:
            print(f"✓ {path} - 状态码: {response.status_code}")
            return True
        else:
            print(f"✗ {path} - 状态码: {response.status_code} (期望: {expected_status})")
            return False
    except requests.RequestException as e:
        print(f"✗ {path} - 请求失败: {e}")
        return False

def check_css_files():
    """检查 CSS 文件是否存在"""
    css_files = [
        'static/css/output.css',
        'static/css/input.css'
    ]
    
    all_exist = True
    for css_file in css_files:
        if os.path.exists(css_file):
            size = os.path.getsize(css_file)
            print(f"✓ {css_file} - 大小: {size} 字节")
        else:
            print(f"✗ {css_file} - 文件不存在")
            all_exist = False
    
    return all_exist

def main():
    print("HaoBBS Tailwind CSS 迁移测试")
    print("=" * 40)
    
    # 检查 CSS 文件
    print("\n1. 检查 CSS 文件:")
    css_ok = check_css_files()
    
    # 检查构建工具
    print("\n2. 检查构建工具:")
    if os.path.exists('package.json'):
        print("✓ package.json 存在")
    else:
        print("✗ package.json 不存在")
    
    if os.path.exists('tailwind.config.js'):
        print("✓ tailwind.config.js 存在")
    else:
        print("✗ tailwind.config.js 不存在")
    
    if os.path.exists('postcss.config.js'):
        print("✓ postcss.config.js 存在")
    else:
        print("✗ postcss.config.js 不存在")
    
    # 如果提供了 URL，测试页面加载
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
        print(f"\n3. 测试页面加载 ({base_url}):")
        
        # 测试主要页面（这些可能需要登录）
        test_pages = [
            ('/', 302),  # 可能重定向到登录页
            ('/login', 200),
        ]
        
        for path, expected_status in test_pages:
            test_page_load(base_url, path, expected_status)
    
    print("\n测试完成!")
    print("\n注意事项:")
    print("- 确保运行 'npm run build-css-prod' 来构建最新的 CSS")
    print("- 在浏览器中测试所有页面的视觉效果")
    print("- 验证响应式设计在移动设备上的表现")

if __name__ == "__main__":
    main()
