# HaoBBS 测试套件

这个目录包含了HaoBBS项目的所有测试文件和工具。

## 目录结构

- `test_suite.py` - 🚀 统一测试套件（推荐使用）
- `data/` - 📊 数据相关测试
  - `create_test_data.py` - 创建测试数据
- `frontend/` - 🎨 前端相关测试
  - `test-styles.py` - 样式和页面测试
- `legacy/` - 📜 历史测试脚本（已整合到统一套件中）
- `utils/` - 🔧 测试工具和辅助脚本

## 使用方法

### 快速测试（推荐）
```bash
# 运行所有测试
python tests/test_suite.py

# 测试指定URL的服务器
python tests/test_suite.py --url http://localhost:5000

# 创建测试数据
python tests/test_suite.py --create-data

# 查看帮助
python tests/test_suite.py --help
```

### 单独测试
```bash
# 创建测试数据
python tests/data/create_test_data.py

# 前端样式测试
python tests/frontend/test-styles.py
```

## 测试内容

### 🔍 自动检测项目
- ✅ 数据库连接和表结构验证
- ✅ 用户认证系统测试
- ✅ 前端文件完整性检查
- ✅ CSS/JS文件存在性验证
- ✅ 配置文件检查

### 🌐 服务器测试（需要URL参数）
- ✅ 页面端点响应测试
- ✅ HTTP状态码验证
- ✅ 基本功能可用性检查

### 📊 数据操作（可选）
- ✅ 测试用户创建
- ✅ 示例帖子生成
- ✅ 测试回复数据

## 测试结果示例

```
HaoBBS 统一测试套件
============================================================

📋 数据库连接测试:
  检查数据库连接...
    ✓ 表 users 存在
    ✓ 表 posts 存在
    ✓ 表 replies 存在
  ✓ 数据库连接和表结构正常

📋 用户认证测试:
  检查用户认证 (admin)...
    ✓ 用户 admin 存在
    ✓ 密码哈希格式正确
    ✓ 密码验证成功: admin

📋 前端文件测试:
  检查前端文件...
    ✓ static/css/output.css (大小: 245678 字节)
    ✓ static/css/input.css (大小: 1234 字节)
    ✓ static/js/post.js
    ✓ static/js/marked.js

============================================================
📊 测试结果汇总:
  数据库连接: ✅ 通过
  用户认证: ✅ 通过
  前端文件: ✅ 通过

总计: 3/3 个测试通过
🎉 所有测试都通过了！
```

## 命令行选项

| 选项 | 说明 | 示例 |
|------|------|------|
| `--url` | 指定测试服务器URL | `--url http://localhost:5000` |
| `--create-data` | 创建测试数据 | `--create-data` |
| `--db` | 指定数据库文件路径 | `--db /path/to/forum.db` |
| `--help` | 显示帮助信息 | `--help` |

## 注意事项

### 🚨 运行前准备
- 确保数据库文件存在（forum.db）
- 前端测试需要先构建CSS：`npm run build-css-prod`
- 服务器测试需要先启动应用：`./scripts/run.sh`

### 📝 测试环境
- Python 3.8+ 
- 依赖包：`requests`, `werkzeug`
- 数据库：SQLite 3

### 🔧 故障排除
```bash
# 如果缺少依赖
pip install requests werkzeug

# 如果数据库不存在，先运行应用创建数据库
python app.py

# 如果CSS文件缺失
npm install
npm run build-css-prod
```

## 从旧测试脚本迁移

如果你之前使用分散的测试脚本，现在可以：

1. **统一使用** `test_suite.py` 获得更好的测试体验
2. **保留使用** 各个子目录中的脚本进行专项测试
3. **参考实现** 在utils目录添加自定义测试工具

## 扩展测试

### 添加新测试
在 `test_suite.py` 中的 `HaoBBSTestSuite` 类添加新的测试方法：

```python
def test_new_feature(self):
    """测试新功能"""
    print("  检查新功能...")
    # 测试逻辑
    return True  # 或 False
```

### 创建专项测试
在对应的子目录中创建新的测试脚本：

```python
#!/usr/bin/env python3
"""
专项测试脚本模板
"""

def main():
    print("执行专项测试...")
    # 测试逻辑

if __name__ == "__main__":
    main()
```

## 持续集成

这个测试套件可以轻松集成到CI/CD流水线中：

```bash
# 在CI中运行
python tests/test_suite.py --db test_forum.db --create-data
```

---

📞 如果有问题或建议，请查看项目主README或提交issue。