#!/usr/bin/env python3
"""
测试脚本整理验证工具
验证测试脚本是否成功整理到正确位置
"""

import os
import sys
from pathlib import Path

def verify_test_migration():
    """验证测试脚本迁移是否成功"""
    print("HaoBBS 测试脚本整理验证")
    print("=" * 50)
    
    # 预期的文件结构
    expected_files = {
        'tests/test_suite.py': '统一测试套件',
        'tests/README.md': '测试说明文档',
        'tests/data/create_test_data.py': '测试数据创建脚本',
        'tests/frontend/test-styles.py': '前端样式测试脚本',
        'tests/frontend/test_favicon.html': 'favicon测试页面',
        'tests/legacy/test_password.py': '密码验证测试脚本',
        'tests/legacy/verify_password.py': '服务器密码验证脚本',
    }
    
    # 应该已清理的文件
    cleaned_files = [
        'create_test_data.py',
        'test_password.py', 
        'verify_password.py',
        'test_favicon.html',
        'scripts/test-styles.py'
    ]
    
    print("\n📁 验证新文件结构:")
    all_ok = True
    
    for file_path, description in expected_files.items():
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✓ {file_path} ({description}) - {size} 字节")
        else:
            print(f"  ✗ {file_path} ({description}) - 文件不存在")
            all_ok = False
    
    print("\n🧹 验证原文件清理:")
    for file_path in cleaned_files:
        if not os.path.exists(file_path):
            print(f"  ✓ {file_path} - 已清理")
        else:
            print(f"  ! {file_path} - 仍然存在")
    
    print("\n📊 目录结构:")
    tests_dir = Path('tests')
    if tests_dir.exists():
        for item in sorted(tests_dir.rglob('*')):
            if item.is_file():
                relative_path = item.relative_to(Path('.'))
                indent = "  " + "  " * (len(item.parts) - 2)
                print(f"{indent}📄 {relative_path}")
            elif item.is_dir() and item != tests_dir:
                relative_path = item.relative_to(Path('.'))
                indent = "  " + "  " * (len(item.parts) - 2)
                print(f"{indent}📁 {relative_path}/")
    
    print("\n" + "=" * 50)
    if all_ok:
        print("🎉 测试脚本整理成功完成！")
        print("\n💡 使用建议:")
        print("  python tests/test_suite.py         # 运行所有测试")
        print("  python tests/test_suite.py --help  # 查看帮助")
        print("  cat tests/README.md                # 查看完整说明")
    else:
        print("⚠️  整理过程中发现问题，请检查缺失的文件")
    
    return all_ok

if __name__ == "__main__":
    success = verify_test_migration()
    sys.exit(0 if success else 1)