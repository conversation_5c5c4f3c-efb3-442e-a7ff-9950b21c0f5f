#!/usr/bin/env python3
import sqlite3
from werkzeug.security import generate_password_hash
from datetime import datetime, timezone, timedelta

DATABASE = 'forum.db'

def get_beijing_time():
    """
    获取当前北京时间
    """
    utc_now = datetime.now(timezone.utc)
    beijing_now = utc_now.astimezone(timezone(timedelta(hours=8)))
    return beijing_now.strftime("%Y-%m-%d %H:%M:%S")

def create_test_data():
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    # 创建测试用户
    password_hash = generate_password_hash('admin')
    print(f"生成的密码哈希: {password_hash}")

    try:
        c.execute('INSERT INTO users (username, password_hash) VALUES (?, ?)',
                 ('admin', password_hash))
        user_id = c.lastrowid
        print(f"创建用户: admin (ID: {user_id})")
    except sqlite3.IntegrityError:
        # 用户已存在，获取用户ID
        user_id = c.execute('SELECT id FROM users WHERE username = ?', ('admin',)).fetchone()[0]
        print(f"用户已存在: admin (ID: {user_id})")
    
    # 创建测试帖子
    test_posts = [
        {
            'title': '欢迎来到HaoBBS论坛！',
            'content': '''# 欢迎使用HaoBBS论坛

这是一个基于Flask开发的简洁论坛系统。

## 主要功能

- **Markdown支持**: 支持完整的Markdown语法
- **分类管理**: 帖子可以按分类组织
- **回复功能**: 支持对帖子进行回复和编辑
- **搜索功能**: 可以搜索帖子内容

## 使用说明

1. 点击右侧的"发布新帖"按钮创建新帖子
2. 使用Markdown语法编写内容
3. 选择合适的分类
4. 发布后可以进行编辑和删除

**祝您使用愉快！**''',
            'category': '公告'
        },
        {
            'title': 'Markdown语法测试帖',
            'content': '''# Markdown语法演示

## 文本格式

**粗体文本** 和 *斜体文本*

## 代码块

```python
def hello_world():
    print("Hello, World!")
    return "欢迎使用HaoBBS"
```

## 列表

### 无序列表
- 项目1
- 项目2
- 项目3

### 有序列表
1. 第一项
2. 第二项
3. 第三项

## 引用

> 这是一个引用块
> 可以包含多行内容

## 表格

| 功能 | 状态 | 说明 |
|------|------|------|
| 发帖 | ✅ | 支持Markdown |
| 回复 | ✅ | 支持编辑删除 |
| 搜索 | ✅ | 全文搜索 |

## 链接

[访问GitHub](https://github.com)

---

这就是Markdown的基本语法演示！''',
            'category': '技术'
        }
    ]
    
    for post_data in test_posts:
        try:
            c.execute('''INSERT INTO posts (title, content, category, created_at)
                        VALUES (?, ?, ?, ?)''',
                     (post_data['title'], post_data['content'], post_data['category'],
                      get_beijing_time()))
            post_id = c.lastrowid
            print(f"创建帖子: {post_data['title']} (ID: {post_id})")
            
            # 为第一个帖子添加一些测试回复
            if post_id == c.execute('SELECT MIN(id) FROM posts').fetchone()[0]:
                test_replies = [
                    '感谢分享！这个论坛看起来很不错。',
                    '支持Markdown真的很方便，可以写出漂亮的格式。',
                    '期待更多功能的加入！'
                ]
                
                for reply_content in test_replies:
                    c.execute('''INSERT INTO replies (content, post_id, created_at)
                                VALUES (?, ?, ?)''',
                             (reply_content, post_id, get_beijing_time()))
                    print(f"  添加回复: {reply_content[:20]}...")
                    
        except sqlite3.IntegrityError as e:
            print(f"帖子可能已存在: {post_data['title']}")
    
    conn.commit()
    conn.close()
    print("测试数据创建完成！")

if __name__ == '__main__':
    create_test_data()
