// 帖子页面JavaScript功能模块

// 草稿存储相关功能
function getDraftKey(postId, replyId = 'new') {
    return `haobbs_draft_post_${postId}_reply_${replyId}`;
}

function saveDraft(postId, replyId, content) {
    if (!content.trim()) {
        // 空内容不保存
        removeDraft(postId, replyId);
        return;
    }

    const draftData = {
        content: content,
        timestamp: Date.now(),
        expires: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7天后过期
    };

    localStorage.setItem(getDraftKey(postId, replyId), JSON.stringify(draftData));
}

function loadDraft(postId, replyId) {
    const key = getDraftKey(postId, replyId);
    const draftJson = localStorage.getItem(key);
    
    if (!draftJson) return null;

    try {
        const draftData = JSON.parse(draftJson);
        
        // 检查是否过期
        if (Date.now() > draftData.expires) {
            localStorage.removeItem(key);
            return null;
        }

        return draftData.content;
    } catch (e) {
        console.error('Failed to parse draft data:', e);
        localStorage.removeItem(key);
        return null;
    }
}

function removeDraft(postId, replyId) {
    localStorage.removeItem(getDraftKey(postId, replyId));
}

// 清理所有过期草稿
function cleanupExpiredDrafts() {
    const now = Date.now();
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('haobbs_draft_post_')) {
            try {
                const draftData = JSON.parse(localStorage.getItem(key));
                if (now > draftData.expires) {
                    localStorage.removeItem(key);
                }
            } catch (e) {
                // 无效数据，直接删除
                localStorage.removeItem(key);
            }
        }
    }
}

// 菜单控制功能
function toggleDropdownMenu(menuId) {
    const menu = document.getElementById(menuId);
    if (menu) {
        // 先关闭所有其他打开的菜单
        document.querySelectorAll('.dropdown-menu').forEach(otherMenu => {
            if (otherMenu.id !== menuId) {
                otherMenu.classList.add('hidden');
            }
        });
        
        // 切换当前菜单
        menu.classList.toggle('hidden');
    }
}

function closeAllDropdownMenus() {
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        menu.classList.add('hidden');
    });
}

// 复制内容功能
function copyPostContent(postId) {
    const postElement = document.getElementById('post-content');
    if (postElement) {
        const content = postElement.textContent || postElement.innerText;
        copyToClipboard(content, '帖子内容已复制到剪贴板');
    }
    closeAllDropdownMenus();
}

function editPost(postId) {
    window.location.href = '/post/' + postId + '/edit';
    closeAllDropdownMenus();
}

function copyReplyContent(replyId) {
    const replyElement = document.getElementById('reply-' + replyId);
    if (replyElement) {
        const content = replyElement.textContent || replyElement.innerText;
        copyToClipboard(content, '回复内容已复制到剪贴板');
    }
    closeAllDropdownMenus();
}

// 通用的复制到剪贴板函数
function copyToClipboard(text, successMessage) {
    // 优先使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showCopyNotification(successMessage);
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(text, successMessage);
        });
    } else {
        // 降级方案：使用传统的 execCommand
        fallbackCopyTextToClipboard(text, successMessage);
    }
}

// 降级的复制方案
function fallbackCopyTextToClipboard(text, successMessage) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // 避免在页面上显示
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopyNotification(successMessage);
        } else {
            showCopyNotification('复制失败，请手动复制');
        }
    } catch (err) {
        console.error('复制失败:', err);
        showCopyNotification('复制失败，请手动复制');
    }
    
    document.body.removeChild(textArea);
}

// 显示复制成功的通知
function showCopyNotification(message) {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: #10b981;
        color: white;
        padding: 12px 24px;
        border-radius: 6px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        font-size: 14px;
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateY(0)';
    }, 10);
    
    // 3秒后自动消失
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-10px)';
        setTimeout(() => {
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// 删除回复处理
function handleDeleteReply(event, replyId) {
    if (!confirm('确定要删除这个回复吗？')) return false;

    fetch('/reply/' + replyId + '/delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => {
        if (response.ok) {
            // 删除成功，移除DOM元素
            const replyElement = document.querySelector('[data-reply-id="' + replyId + '"]');
            if (replyElement) {
                replyElement.remove();
                // 显示成功通知
                showCopyNotification('回复已删除');
            }
            // 关闭下拉菜单
            closeAllDropdownMenus();
        } else {
            throw new Error('删除失败');
        }
    })
    .catch(error => {
        console.error('删除回复失败:', error);
        alert('删除失败，请重试');
    });
    
    return false;
}

// 删除帖子处理
function handleDeletePost(event, postId) {
    event.preventDefault();
    if (!confirm('确定要删除这个帖子吗？')) return;

    fetch('/post/' + postId + '/delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '/';
            } else {
                alert(data.message);
            }
        });
}


// 字符统计函数（只统计中文、英文和数字）
function countAllCharacters(text) {
    // 使用正则表达式匹配中文、英文和数字字符
    const validChars = text.match(/[\u4e00-\u9fa5a-zA-Z0-9]/g);
    return validChars ? validChars.length : 0;
}

// 更新字数统计
function updateCharacterCount(textarea, charCount) {
    const text = textarea.value;
    const count = countAllCharacters(text);
    charCount.textContent = count + ' 字';
}

// 更新选中统计
function updateSelectionCount(textarea, selectionCount) {
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    
    if (start === end) {
        // 没有选中文本
        selectionCount.textContent = '选中: 0 字';
        selectionCount.classList.add('empty');
        selectionCount.classList.remove('highlight');
    } else {
        // 有选中文本，统计其中的字符
        const selectedText = textarea.value.substring(start, end);
        const count = countAllCharacters(selectedText);
        selectionCount.textContent = '选中: ' + count + ' 字';
        selectionCount.classList.remove('empty');
        
        // 如果选中文本包含字符，高亮显示
        if (count > 0) {
            selectionCount.classList.add('highlight');
        } else {
            selectionCount.classList.remove('highlight');
        }
    }
}

// 初始化回复框字数统计
function initReplyCharacterCount() {
    const replyTextarea = document.getElementById('reply-content');
    const replyCharCount = document.getElementById('reply-character-count');
    const replySelectionCount = document.getElementById('reply-selection-count');

    if (replyTextarea && replyCharCount && replySelectionCount) {
        // 初始化字数统计
        updateCharacterCount(replyTextarea, replyCharCount);
        updateSelectionCount(replyTextarea, replySelectionCount);

        // 监听输入事件实时更新字数
        replyTextarea.addEventListener('input', () => {
            updateCharacterCount(replyTextarea, replyCharCount);
            updateSelectionCount(replyTextarea, replySelectionCount);
            
            // 自动保存草稿
            const replyId = document.getElementById('edit-reply-id').value || 'new';
            const postId = window.postPageData.postId;
            saveDraft(postId, replyId, replyTextarea.value);
        });
        
        replyTextarea.addEventListener('propertychange', () => updateCharacterCount(replyTextarea, replyCharCount));

        // 监听选中相关事件
        replyTextarea.addEventListener('select', () => updateSelectionCount(replyTextarea, replySelectionCount));
        replyTextarea.addEventListener('selectionchange', () => updateSelectionCount(replyTextarea, replySelectionCount));
        replyTextarea.addEventListener('mouseup', () => updateSelectionCount(replyTextarea, replySelectionCount));
        replyTextarea.addEventListener('keyup', () => updateSelectionCount(replyTextarea, replySelectionCount));
    }
}


// 嵌入式回复框不需要切换显示/隐藏功能
// 保留resetToNewReplyMode函数供其他功能使用

// 重置为新回复模式
function resetToNewReplyMode() {
    // 先保存当前是否处于编辑状态
    const wasEditingReply = currentEditReplyId !== null;
    
    // 清空编辑相关状态
    document.getElementById('edit-reply-id').value = '';
    currentEditReplyId = null;
    
    // 恢复标题为发表回复模式
    document.getElementById('reply-title').textContent = '发表回复';
    
    // 无论之前是否在编辑状态，都要重置为新回复模式
    const replyContent = document.getElementById('reply-content');
    const postId = window.postPageData.postId;
    const newReplyDraft = loadDraft(postId, 'new');
    replyContent.value = newReplyDraft || '';
    
    // 更新字数统计
    const replyCharCount = document.getElementById('reply-character-count');
    const replySelectionCount = document.getElementById('reply-selection-count');
    updateCharacterCount(replyContent, replyCharCount);
    updateSelectionCount(replyContent, replySelectionCount);
}

// 嵌入式回复框不需要关闭功能

let currentEditReplyId = null;

// 开始编辑回复
async function startEditReply(replyId) {
    try {
        let content = '';
        
        // 首先尝试从本地存储加载草稿
        const postId = window.postPageData.postId;
        const draftContent = loadDraft(postId, replyId);
        if (draftContent) {
            content = draftContent;
        } else {
            // 如果没有草稿，从服务器获取原始内容
            const response = await fetch(`/reply/${replyId}/edit`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                content = data.content;
            } else {
                // 如果获取失败，使用页面上的文本内容作为备选
                const replyContentElement = document.getElementById(`reply-${replyId}`);
                content = replyContentElement.textContent;
            }
        }

        // 填充回复框
        document.getElementById('reply-content').value = content;
        document.getElementById('edit-reply-id').value = replyId;
        currentEditReplyId = replyId;

        // 更新标题为编辑模式
        document.getElementById('reply-title').textContent = '编辑回复';
        
        // 聚焦到文本区域
        setTimeout(() => {
            document.getElementById('reply-content').focus();
        }, 100);

    } catch (error) {
        console.error('获取原始内容失败:', error);
        alert('获取回复内容失败，请重试');
    }
}

// 取消编辑
function cancelEditReply() {
    // 使用统一的重置函数
    resetToNewReplyMode();
}

// 处理回复提交（新建和编辑）
async function handleReplySubmit(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const replyId = document.getElementById('edit-reply-id').value;
    const isEditMode = replyId !== '';
    const postId = window.postPageData.postId;

    try {
        let response;
        if (isEditMode) {
            // 编辑现有回复
            response = await fetch(`/reply/${replyId}/edit`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            });
        } else {
            // 新建回复
            response = await fetch(`/reply/${postId}`, {
                method: 'POST',
                body: formData
            });
        }

        if (response.ok) {
            const data = await response.json();

            // 提交成功后删除对应的草稿
            removeDraft(postId, isEditMode ? replyId : 'new');

            if (isEditMode) {
                // 更新页面中的回复内容
                const replyElement = document.querySelector(`[data-reply-id="${data.id}"]`);
                if (replyElement) {
                    const contentElement = replyElement.querySelector(`#reply-${data.id}`);
                    if (contentElement) {
                        contentElement.innerHTML = data.content;
                    }
                    
                    // 更新字数统计
                    const wordCountElement = replyElement.querySelector('.flex.items-center.gap-3 span:first-child');
                    if (wordCountElement) {
                        // 使用JavaScript计算字数，保证与后端算法一致
                        const wordCount = countAllCharacters(formData.get('content') || '');
                        wordCountElement.textContent = `共 ${wordCount} 字`;
                    }
                }
                // 重置编辑状态
                cancelEditReply();
            } else {
                // 新建回复成功后跳转到最后一页
                const postId = window.postPageData.postId;
                window.location.href = `/post/${postId}?page=last`;
            }
        } else {
            throw new Error('提交失败');
        }
    } catch (error) {
        alert(error.message || '提交失败，请重试');
    }
}

// 页面初始化函数
function initPostPage() {
    // 初始化回复框字数统计
    initReplyCharacterCount();

    // 绑定菜单按钮点击事件
    document.addEventListener('click', function(event) {
        // 菜单按钮点击处理
        if (event.target.closest('.menu-btn')) {
            event.preventDefault();
            event.stopPropagation();
            const menuBtn = event.target.closest('.menu-btn');
            const menuId = menuBtn.dataset.menu;
            toggleDropdownMenu(menuId);
            return;
        }

        // 编辑按钮点击处理（在下拉菜单内）
        if (event.target.matches('.dropdown-item[data-reply-id]')) {
            event.preventDefault();
            const replyId = event.target.dataset.replyId;
            startEditReply(replyId);
            closeAllDropdownMenus();
            return;
        }

        // 点击其他地方关闭所有菜单
        closeAllDropdownMenus();
    });


    // 嵌入式回复框不需要悬浮按钮和关闭按钮的事件监听器

    // 绑定表单提交处理
    const replyForm = document.getElementById('reply-form');
    if (replyForm) {
        replyForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleReplySubmit(e);
        });
    }

    // 清理过期草稿
    cleanupExpiredDrafts();

    // 页面加载时自动恢复新回复的草稿
    const postId = window.postPageData.postId;
    const newReplyDraft = loadDraft(postId, 'new');
    if (newReplyDraft) {
        document.getElementById('reply-content').value = newReplyDraft;
        // 更新字数统计
        const replyTextarea = document.getElementById('reply-content');
        const replyCharCount = document.getElementById('reply-character-count');
        const replySelectionCount = document.getElementById('reply-selection-count');
        updateCharacterCount(replyTextarea, replyCharCount);
        updateSelectionCount(replyTextarea, replySelectionCount);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initPostPage);
