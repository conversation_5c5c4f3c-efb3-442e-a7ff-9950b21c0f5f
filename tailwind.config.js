/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./templates/**/*.html",
    "./static/**/*.js",
    "./static/**/*.html"
  ],
  // 启用 JIT 模式以获得更小的构建输出
  mode: 'jit',
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          500: '#007bff',
          600: '#0056b3',
          700: '#0062cc'
        },
        success: {
          500: '#28a745',
          600: '#218838',
          700: '#1e7e34'
        },
        danger: {
          500: '#dc3545',
          600: '#c82333'
        }
      },
      fontFamily: {
        sans: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif']
      },
      boxShadow: {
        'card': '0 2px 8px rgba(0,0,0,0.05)',
        'card-hover': '0 4px 12px rgba(0,0,0,0.1)',
        'modal': '0 4px 16px rgba(0,0,0,0.2)'
      }
    },
  },
  plugins: [],
}

