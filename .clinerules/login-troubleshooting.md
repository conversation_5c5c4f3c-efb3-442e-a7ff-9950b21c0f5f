## Brief overview
本规则文件包含 HaoBBS 项目中登录问题的排查流程和维护指南，基于实际遇到的登录问题排查经验总结。当遇到用户无法登录的情况时，应按照此规则进行系统性排查。

## 问题排查流程
- 首先验证用户信息是否存在且密码哈希值正确
  - 检查数据库中用户记录
  - 验证密码哈希算法和值的匹配性
- 检查系统配置和网络连接
  - 确认反向代理配置正确（如 Caddy 指向正确端口）
  - 验证应用服务在指定端口正常监听
  - 检查进程管理工具状态（如 pm2）
- 验证代码逻辑正确性
  - 检查登录页面模板渲染正常
  - 验证登录路由处理逻辑
  - 在服务器端添加调试日志确认验证流程
- 进行网络连接测试
  - 使用 curl 等工具测试网络连通性
  - 模拟登录过程验证后端响应
  - 使用获取的 cookies 测试会话访问

## 客户端问题识别
- 当后端功能验证正常但用户仍无法登录时，应考虑客户端问题
- 常见客户端问题包括：
  - 浏览器缓存和 Cookie 冲突
  - 浏览器扩展阻止 Cookie 或 JavaScript
  - 网络连接不稳定
- 解决方案：
  - 清除浏览器缓存和 Cookie
  - 使用隐私/无痕模式登录
  - 检查并禁用可能的浏览器扩展
  - 尝试不同浏览器或设备

## 调试和日志
- 在登录相关代码中添加详细的调试信息
- 记录关键步骤的日志：
  - 登录尝试（用户名、时间）
  - 用户查找结果
  - 密码验证结果
  - 重定向操作
- 通过日志快速定位问题所在

## 验证方法
- 使用命令行工具进行功能验证：
  - curl 测试网络连接和 HTTP 响应
  - 模拟表单提交测试登录接口
  - 使用 cookies 测试会话状态
- 通过服务器日志确认用户实际访问情况

## 维护建议
- 定期检查服务器日志，及时发现异常
- 建立数据库备份机制，保护用户数据
- 保持应用和依赖库及时更新
- 记录常见问题的解决方案，建立知识库

## 项目特定信息
- 服务器地址：**************
- 应用端口：5002
- 数据库文件：forum.db
- 管理用户凭证：admin/admin2025
- 普通用户凭证：haoxueren/060e0f01
- 反向代理配置：bbs.haoxueren.com 指向 localhost:5002
