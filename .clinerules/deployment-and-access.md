## Brief overview
本规则文件包含 HaoBBS 项目的部署流程和访问配置指南。该文件旨在为项目的持续部署和访问管理提供标准化的操作规范。

## 部署流程
- 使用 `scripts/deploy.sh` 脚本进行自动化部署
- 部署脚本会自动同步本地代码到服务器
- 部署过程包括：
  - 创建远程部署目录
  - 同步服务器数据库到本地作为备份
  - 同步项目文件到服务器（排除不必要的文件和目录）
  - 检查并安装pm2进程管理器
  - 在服务器上创建Python虚拟环境
  - 安装项目依赖（flask, werkzeug等）
  - 检查并清理端口占用
  - 使用pm2管理应用服务

## 访问配置
- 应用仅通过域名访问：bbs.haoxueren.com
- 禁止直接通过IP和端口访问应用
- 服务器地址：**************
- 应用端口：5002
- 反向代理配置应将域名请求转发到本地端口5002

## 服务器管理
- 使用pm2进行应用进程管理
- 应用名称：haobbs
- pm2会自动保存配置，确保服务器重启后应用自动启动
- 定期检查pm2状态确保应用正常运行

## 部署脚本配置
- 服务器用户：lighthouse
- 服务器主机：**************
- SSH端口：22
- 远程部署目录：/home/<USER>/flask/haobbs
- 部署脚本会自动排除以下文件和目录：
  - .git
  - __pycache__
  - *.pyc
  - venv/
  - backup/
  - forum.db
  - node_modules/

## 数据库管理
- 部署时会自动备份服务器数据库到本地
- 备份文件命名格式：forum_YYYYMMDD.db
- 备份目录：./backup/
- 如果服务器上不存在数据库文件，部署脚本会自动初始化

## 故障排除
- 部署失败时检查SSH连接配置
- 确保本地具有rsync工具
- 检查服务器磁盘空间和权限
- 验证pm2是否正确安装和配置
