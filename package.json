{"name": "haobbs", "version": "1.0.0", "description": "HaoBBS Forum Application", "main": "app.py", "scripts": {"build-css": "tailwindcss -i ./static/css/input.css -o ./static/css/output.css --watch", "build-css-prod": "tailwindcss -i ./static/css/input.css -o ./static/css/output.css --minify", "dev": "npm run build-css", "build": "npm run build-css-prod"}, "keywords": ["forum", "flask", "tailwindcss"], "author": "HaoBBS Team", "license": "MIT", "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.0"}, "dependencies": {"dompurify": "^3.2.6", "marked": "^16.2.0"}}