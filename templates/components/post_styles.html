<!-- 帖子页面样式组件 -->
<style>
    /* Markdown内容样式 */
    .markdown-content h1, .markdown-content h2, .markdown-content h3,
    .markdown-content h4, .markdown-content h5, .markdown-content h6 {
        margin: 1.5em 0 0.5em 0;
        font-weight: bold;
        line-height: 1.2;
    }
    .markdown-content h1 { font-size: 1.8em; color: #2563eb; }
    .markdown-content h2 { font-size: 1.5em; color: #3b82f6; }
    .markdown-content h3 { font-size: 1.3em; color: #6366f1; }
    .markdown-content h4 { font-size: 1.1em; color: #8b5cf6; }
    .markdown-content h5, .markdown-content h6 { font-size: 1em; color: #a855f7; }

    .markdown-content p { margin: 0.8em 0; line-height: 1.6; }
    .markdown-content ul, .markdown-content ol { margin: 0.4em 0; padding-left: 1em; }
    .markdown-content ul { list-style-type: disc; }
    .markdown-content ol { list-style-type: decimal; }
    .markdown-content li { margin: 1em 0; }

    /* 进一步优化段落间距 */
    .markdown-content p + p { margin-top: 0.8em; }
    .markdown-content p:first-child { margin-top: 0; }
    .markdown-content p:last-child { margin-bottom: 0; }

    .markdown-content blockquote {
        border-left: 4px solid #e5e7eb;
        margin: 1em 0;
        padding: 0.5em 1em;
        background-color: #f9fafb;
    }

    .markdown-content code {
        background-color: #f3f4f6;
        padding: 0.2em 0.4em;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
    }

    .markdown-content pre {
        background-color: #1f2937;
        color: #f9fafb;
        padding: 1em;
        border-radius: 6px;
        overflow-x: auto;
        margin: 1em 0;
    }

    .markdown-content pre code {
        background-color: transparent;
        padding: 0;
        color: inherit;
    }

    .markdown-content table {
        border-collapse: collapse;
        width: 100%;
        margin: 1em 0;
    }

    .markdown-content th, .markdown-content td {
        border: 1px solid #d1d5db;
        padding: 0.5em;
        text-align: left;
    }

    .markdown-content th {
        background-color: #f3f4f6;
        font-weight: bold;
    }

    .markdown-content a {
        color: #2563eb;
        text-decoration: underline;
    }

    .markdown-content a:hover {
        color: #1d4ed8;
    }

    .markdown-content hr {
        border: none;
        border-top: 2px solid #e5e7eb;
        margin: 2em 0;
    }

    .markdown-content img {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        margin: 0.5em 0;
    }

    /* 待办事项样式 */
    .markdown-content .task-list-item {
        list-style: none;
        margin: 0.8em 0;
        padding: 0.3em 0;
        display: flex;
        align-items: flex-start;
        line-height: 1.6;
    }

    .markdown-content .task-list-item input[type="checkbox"] {
        margin-right: 0.6em;
        margin-top: 0.3em;
        flex-shrink: 0;
        transform: scale(1.1);
        accent-color: #2563eb;
    }

    /* 未完成任务样式 */
    .markdown-content .task-list-item:has(input[type="checkbox"]:not(:checked)) {
        color: #374151;
        opacity: 1;
    }

    /* 已完成任务样式 */
    .markdown-content .task-list-item:has(input[type="checkbox"]:checked) {
        color: #9ca3af;
        opacity: 0.75;
    }

    /* 兼容不支持 :has() 的浏览器 */
    .markdown-content .task-list-item input[type="checkbox"]:checked ~ * {
        color: #9ca3af;
    }

    /* 悬浮回复按钮样式已移除 - 改用嵌入式回复框设计 */
</style>
