<!-- 帖子内容组件 - 包含正文内容、编辑删除按钮、字数统计 -->
<div class="card mb-4 border border-gray-200">
    <div class="text-gray-500 text-sm mb-2 flex justify-between items-center">
        <span>{{ post.created_at[:16] }}</span>
        <div class="flex items-center gap-3">
            <span>共 {{ post.content|word_count }} 字</span>
            <div class="relative">
                <button class="menu-btn p-1 hover:bg-gray-100 rounded" data-menu="post-menu">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="5" r="2" fill="currentColor"/>
                        <circle cx="12" cy="12" r="2" fill="currentColor"/>
                        <circle cx="12" cy="19" r="2" fill="currentColor"/>
                    </svg>
                </button>
                <div id="post-menu" class="dropdown-menu hidden absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg z-10 py-1 min-w-20">
                    <button onclick="editPost('{{ post.id }}')" class="dropdown-item block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">编辑</button>
                    <button onclick="copyPostContent('{{ post.id }}')" class="dropdown-item block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">复制</button>
                    <!-- 只在无回复时显示删除按钮 -->
                    {% if replies|length == 0 %}
                    <button onclick="handleDeletePost(event, '{{ post.id }}')" class="dropdown-item-danger">删除</button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    <div class="content-text markdown-content my-4" id="post-content">{{ post.content|markdown|safe }}</div>

</div>
