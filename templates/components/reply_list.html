<!-- 回复列表组件 - 包含回复列表的显示和相关操作 -->
<div id="replies-list">
    {% for reply in replies %}
    <div class="card mb-4 border border-gray-200" data-reply-id="{{ reply.id }}">
        <div class="text-gray-500 text-sm mb-2 flex justify-between items-center">
            <span>{{ reply.created_at[:16] }}</span>
            <div class="flex items-center gap-3">
                <span>共 {{ reply.content|word_count }} 字</span>
                <div class="relative">
                <button class="menu-btn p-1 hover:bg-gray-100 rounded" data-menu="reply-menu-{{ reply.id }}">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="5" r="2" fill="currentColor"/>
                        <circle cx="12" cy="12" r="2" fill="currentColor"/>
                        <circle cx="12" cy="19" r="2" fill="currentColor"/>
                    </svg>
                </button>
                <div id="reply-menu-{{ reply.id }}" class="dropdown-menu hidden absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg z-10 py-1 min-w-20">
                    <button class="dropdown-item block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100" data-reply-id="{{ reply.id }}">编辑</button>
                    <button onclick="copyReplyContent('{{ reply.id }}')" class="dropdown-item block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">复制</button>
                    <button onclick="handleDeleteReply(event, '{{ reply.id }}');" class="dropdown-item-danger">删除</button>
                </div>
                </div>
            </div>
        </div>
        <div class="content-text markdown-content my-2 py-1 text-justify" id="reply-{{ reply.id }}">{{ reply.content|markdown|safe }}</div>
    </div>
    {% endfor %}
</div>
