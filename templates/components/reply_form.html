<!-- 回复框组件 - 嵌入式回复框 -->

<!-- 嵌入式回复框 -->
<div class="embedded-reply-container" id="reply-container">
    <div class="embedded-reply-box">
        <form id="reply-form" method="post" class="reply-form">
            <!-- 标题栏 -->
            <div class="flex justify-between items-center mb-3 px-1">
                <!-- 左侧：标题 -->
                <h4 id="reply-title" class="text-base font-semibold text-gray-800 m-0">发表回复</h4>

                <!-- 右侧：提交按钮 -->
                <button type="submit" class="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200" title="提交回复">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                        <line x1="22" y1="2" x2="11" y2="13"></line>
                        <polygon points="22,2 15,22 11,13 2,9"></polygon>
                    </svg>
                    提交
                </button>
            </div>
            <div class="relative-container">
                <textarea name="content" id="reply-content" placeholder="支持Markdown语法，如：**粗体** *斜体* 等..." required class="textarea-reply"></textarea>
                <div class="absolute-bottom-right">
                    <span id="reply-selection-count" class="selection-count empty text-xs text-gray-500 px-1 rounded">选中: 0 字</span>
                    <span id="reply-character-count" class="character-count text-xs text-gray-500 px-1 rounded">0 字</span>
                </div>
            </div>
            <!-- 隐藏字段用于存储编辑的回复ID -->
            <input type="hidden" id="edit-reply-id" name="edit_reply_id" value="">
        </form>
    </div>
</div>
