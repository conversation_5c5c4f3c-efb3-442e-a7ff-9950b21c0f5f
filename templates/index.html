<!DOCTYPE html>
<html>

<head>
    <title>我的论坛</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.svg') }}" type="image/svg+xml">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/output.css') }}">
    <!-- Markdown样式 -->
    <style>
        .markdown-preview {
            color: #6b7280;
            line-height: 1.5;
        }
        .markdown-preview h1, .markdown-preview h2, .markdown-preview h3,
        .markdown-preview h4, .markdown-preview h5, .markdown-preview h6 {
            margin: 0;
            font-weight: 600;
            color: #374151;
        }
        .markdown-preview p { margin: 0.3em 0; }
        .markdown-preview code {
            background-color: #f3f4f6;
            padding: 0.1em 0.3em;
            border-radius: 2px;
            font-size: 0.85em;
        }
        .markdown-preview strong { font-weight: normal; color: #374151; }
        .markdown-preview em { font-style: italic; }
        
    </style>
</head>

<body class="font-sans m-0 p-0 leading-relaxed">
    <!-- 引入侧边栏模块 -->
    {% include 'components/sidebar.html' %}
    
    <!-- 主内容区域 -->
    <div class="main-content" id="mainContent">
        <div class="w-[95%] max-w-3xl mx-auto my-5 px-4 md:w-full md:my-8 md:px-6">

            <!-- 帖子列表保持不变 -->
            {% for post in posts %}
            <a href="{{ url_for('view_post', post_id=post.id) }}" class="block no-underline">
                <div class="card mb-4 border border-gray-200 hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 cursor-pointer">
                    <div class="flex flex-col gap-3">
                        <div class="flex justify-between items-start gap-4">
                            <h3 class="m-0 text-lg leading-snug text-gray-900 hover:text-primary-500 transition-colors duration-200">
                                {{ post.title }}
                            </h3>
                            <time class="text-gray-500 text-sm whitespace-nowrap flex-shrink-0">{{ post.created_at[:16] }}</time>
                        </div>

                        <!-- 新增内容摘要 -->
                        {% if post.content %}
                        <div class="text-ellipsis-2 text-gray-600 m-0 text-sm leading-relaxed markdown-preview">
                            {{ post.content | markdown_truncate(150, true, '...') | safe }}
                        </div>
                        <div class="content-meta-index">
                            字数：{{ post.content|word_count }} 字
                        </div>
                        {% endif %}
                    </div>
                </div>
            </a>
            {% endfor %}
        </div>
    </div>
</body>

</html>
