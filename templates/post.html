<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的论坛 - {{ post.title }}</title>
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.svg') }}" type="image/svg+xml">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/output.css') }}">

    <!-- 引入样式组件 -->
    {% include 'components/post_styles.html' %}
    <!-- 页面数据传递给JavaScript -->
    <script>
        window.postPageData = {
            postId: '{{ post.id }}',
            page: {{ page }}
        };
    </script>

    <!-- 引入JavaScript功能模块 -->
    <script src="{{ url_for('static', filename='js/post.js') }}"></script>
</head>

<body class="font-sans m-0 p-0 leading-relaxed">
    {% include 'components/sidebar.html' %}
    <div class="main-content" id="mainContent">
        <div class="w-[95%] max-w-3xl mx-auto my-5 px-4 md:w-full md:my-8 md:px-6">

            <!-- 帖子头部组件 -->
            {% include 'components/post_header.html' %}

            <!-- 帖子内容组件 -->
            {% include 'components/post_content.html' %}

            <!-- 上方分页组件 - 双分页设计 -->
            {% if total_pages > 1 %}
            <div class="pagination-top mb-4">
                {% include 'components/pagination.html' %}
            </div>
            {% endif %}

            <!-- 回复列表组件 -->
            {% include 'components/reply_list.html' %}

            <!-- 下方分页组件 - 双分页设计 -->
            {% if total_pages > 1 %}
            <div class="pagination-bottom mt-4 mb-6">
                {% include 'components/pagination.html' %}
            </div>
            {% endif %}

            <!-- 回复框组件 - 仅在最后一页或单页时显示 -->
            {% if page == total_pages or total_pages <= 1 %}
            {% include 'components/reply_form.html' %}
            {% else %}
            <!-- 非最后一页的提示 -->
            <div class="text-center mt-6 mb-4 p-6 bg-gray-50 rounded-lg border border-gray-200">
                <div class="flex flex-col items-center">
                    <svg class="w-12 h-12 text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <h4 class="text-lg font-semibold text-gray-700 mb-2">想要发表回复？</h4>
                    <p class="text-gray-600 mb-4">新回复将添加到讨论的最后，请前往最后一页发表您的观点</p>
                    <a href="{{ url_for('view_post', post_id=post.id, page=total_pages) }}"
                       class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-sm hover:shadow-md">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                        前往最后一页回复
                    </a>
                    <p class="text-sm text-gray-500 mt-3">当前第 {{ page }} 页，共 {{ total_pages }} 页</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>



</body>

</html>
