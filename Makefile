# Makefile for HaoBBS Docker operations

# Variables
IMAGE_NAME = haobbs
CONTAINER_NAME = haobbs-app
PORT = 5002

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
NC = \033[0m # No Color

# Help target
.PHONY: help
help:
	@echo "HaoBBS Docker Makefile"
	@echo "======================"
	@echo "Available commands:"
	@echo "  build      - Build Docker image"
	@echo "  run        - Run container"
	@echo "  start      - Start container"
	@echo "  stop       - Stop container"
	@echo "  logs       - View container logs"
	@echo "  clean      - Remove container and image"
	@echo "  compose-up - Start with docker-compose"
	@echo "  compose-dev - Start development environment"
	@echo "  compose-down - Stop docker-compose"

# Build Docker image
.PHONY: build
build:
	@echo "$(GREEN)Building Docker image...$(NC)"
	docker build -t $(IMAGE_NAME) .

# Run container
.PHONY: run
run:
	@echo "$(GREEN)Running container...$(NC)"
	docker run -d \
		--name $(CONTAINER_NAME) \
		-p $(PORT):$(PORT) \
		-v $(PWD)/forum.db:/app/forum.db \
		$(IMAGE_NAME)

# Start container
.PHONY: start
start:
	@echo "$(GREEN)Starting container...$(NC)"
	docker start $(CONTAINER_NAME)

# Stop container
.PHONY: stop
stop:
	@echo "$(YELLOW)Stopping container...$(NC)"
	docker stop $(CONTAINER_NAME)

# View logs
.PHONY: logs
logs:
	@echo "$(GREEN)Viewing container logs...$(NC)"
	docker logs -f $(CONTAINER_NAME)

# Clean up
.PHONY: clean
clean:
	@echo "$(YELLOW)Cleaning up...$(NC)"
	-docker stop $(CONTAINER_NAME)
	-docker rm $(CONTAINER_NAME)
	-docker rmi $(IMAGE_NAME)

# Docker Compose operations
.PHONY: compose-up
compose-up:
	@echo "$(GREEN)Starting with docker-compose...$(NC)"
	docker-compose up --build

.PHONY: compose-dev
compose-dev:
	@echo "$(GREEN)Starting development environment...$(NC)"
	docker-compose --profile dev up --build

.PHONY: compose-down
compose-down:
	@echo "$(YELLOW)Stopping docker-compose...$(NC)"
	docker-compose down

# Default target
.PHONY: default
default: help
