# HaoBBS 部署脚本说明

本目录包含了 HaoBBS 项目的各种部署和启动脚本。

## 脚本概览

### 🚀 启动脚本

#### `run.sh` - Docker启动脚本
**推荐用于本地开发和测试**

```bash
# 赋予执行权限
chmod +x scripts/run.sh

# 开发模式启动（默认，启用热重载）
./scripts/run.sh

# 生产模式启动（禁用热重载）
./scripts/run.sh --prod
```

**功能特性：**
- ✅ 使用Docker运行项目，确保环境一致性
- ✅ 自动检查Docker和Docker Compose环境
- ✅ 支持开发和生产模式
- ✅ 开发模式启用代码热重载
- ✅ 彩色日志输出

### 🚢 部署脚本

#### `deploy.sh` - 远程服务器部署脚本
**用于部署到远程服务器**

```bash
# 部署到远程服务器
./scripts/deploy.sh
```

**功能特性：**
- ✅ 自动同步数据库备份
- ✅ 上传项目文件到服务器
- ✅ 远程环境配置
- ✅ Docker容器化部署
- ✅ 自动安装Docker和Docker Compose

### 🛠️ 工具脚本

- `add_user.py` - 添加用户脚本
- `add_user_cli.py` - 命令行添加用户
- `backup.py` - 数据库备份脚本
- `test-styles.py` - 样式测试脚本

## 使用建议

### 本地开发
```bash
# Docker启动（开发模式，启用热重载）
./scripts/run.sh
```

### 生产部署
```bash
# 远程部署到服务器
./scripts/deploy.sh
```

## 系统要求

- **Docker**: 20.10 或更高版本
- **Docker Compose**: 1.29 或更高版本
- **操作系统**: macOS, Linux, Windows（支持Docker的系统）

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 手动清理端口
   lsof -t -i:5002 | xargs kill -9
   ```

2. **Docker容器问题**
   ```bash
   # 停止所有容器
   docker-compose down
   ```

3. **CSS 构建失败**
   ```bash
   # 重新安装 Node.js 依赖
   rm -rf node_modules package-lock.json
   npm install
   npm run build-css-prod
   ```

4. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x scripts/*.sh
   ```

### 日志和调试

所有脚本都包含详细的日志输出：
- 🔵 **[INFO]** - 信息日志
- 🟢 **[SUCCESS]** - 成功日志
- 🔴 **[ERROR]** - 错误日志
- 🟡 **[WARNING]** - 警告日志

## 配置说明

### 环境变量
脚本会自动检测和配置以下环境：
- `PROJECT_ROOT` - 项目根目录

### 端口配置
- 默认端口：`5002`
- 可在 `app.py` 中修改

### 数据库
- 数据库文件：`forum.db`
- 备份目录：`backup/`

## 更新日志

### v1.0.0 (2025-08-19)
- ✅ 创建Docker启动脚本 `run.sh`
- ✅ 创建远程部署脚本 `deploy.sh`
- ✅ 修复部署脚本中的环境配置问题
- ✅ 添加完整的错误处理和状态检查
- ✅ 支持开发和生产模式
- ✅ 添加清理和重置功能
