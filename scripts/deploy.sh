#!/bin/bash

# 服务器配置
SERVER_USER="lighthouse"
SERVER_HOST="**************"
SERVER_PORT="22"
REMOTE_DIR="/home/<USER>/flask/haobbs"

# 颜色输出函数
function echo_info() {
    echo -e "\033[34m[INFO] $1\033[0m"
}

function echo_success() {
    echo -e "\033[32m[SUCCESS] $1\033[0m"
}

function echo_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

# 检查命令是否执行成功
function check_status() {
    if [ $? -eq 0 ]; then
        echo_success "$1"
    else
        echo_error "$2"
        exit 1
    fi
}

# 确保远程目录存在
echo_info "创建远程部署目录..."
ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "mkdir -p $REMOTE_DIR"
check_status "远程目录创建成功" "远程目录创建失败"

# 同步服务器数据库到本地
echo_info "同步服务器数据库到本地..."
TIMESTAMP=$(date +'%Y%m%d')
rsync -avz -e "ssh -p $SERVER_PORT" $SERVER_USER@$SERVER_HOST:$REMOTE_DIR/forum.db ./backup/forum_$TIMESTAMP.db
check_status "数据库同步成功" "数据库同步失败"

# 临时重命名本地数据库文件，避免在同步时覆盖服务器数据库
if [ -f "forum.db" ]; then
    echo_info "临时重命名本地数据库文件..."
    mv forum.db forum.db.local
    LOCAL_DB_MOVED=true
fi

# 同步项目文件（使用 Docker 部署，包含 Docker 相关文件）
echo_info "同步项目文件到服务器..."
rsync -avz --exclude '.git' \
    --exclude '__pycache__' \
    --exclude '*.pyc' \
    --exclude 'backup/' \
    --exclude 'node_modules/' \
    --exclude 'forum.db' \
    --exclude 'forum.db.local' \
    -e "ssh -p $SERVER_PORT" \
    ./ $SERVER_USER@$SERVER_HOST:$REMOTE_DIR/
check_status "文件同步成功" "文件同步失败"

# 恢复本地数据库文件
if [ "$LOCAL_DB_MOVED" = true ]; then
    echo_info "恢复本地数据库文件..."
    mv forum.db.local forum.db
fi

# 在服务器上执行 Docker 部署命令
ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST << ENDSSH
    cd $REMOTE_DIR
    
    # 检查并安装 Docker
    echo "检查 Docker 安装..."
    if ! command -v docker &> /dev/null; then
        echo "Docker 未安装，正在安装..."
        # Ubuntu/Debian 系统
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y docker.io
            sudo usermod -aG docker $USER
        # CentOS/RHEL 系统
        elif command -v yum &> /dev/null; then
            sudo yum install -y docker
            sudo usermod -aG docker $USER
        else
            echo "不支持的操作系统，请手动安装 Docker"
            exit 1
        fi
        sudo systemctl start docker
        sudo systemctl enable docker
    else
        echo "Docker 已安装"
    fi
    
    # 检查并安装 Docker Compose
    echo "检查 Docker Compose 安装..."
    if ! command -v docker-compose &> /dev/null; then
        echo "Docker Compose 未安装，正在安装..."
        sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose
    else
        echo "Docker Compose 已安装"
    fi
    
    # 检查数据库是否存在，如果不存在则创建空的数据库文件
    if [ ! -f "forum.db" ]; then
        echo "数据库文件不存在，创建空的数据库文件..."
        touch forum.db
    else
        echo "数据库文件已存在"
    fi
    
    # 停止现有的容器并清理相关资源
    echo "停止现有的容器并清理相关资源..."
    docker-compose down 2>/dev/null || true
    
    # 清理相关的 Docker 网络
    echo "清理相关的 Docker 网络..."
    docker network prune -f 2>/dev/null || true
    
    # 检查并清理端口占用
    echo "检查端口占用情况..."
    if lsof -i:5002 > /dev/null 2>&1; then
        echo "端口5002被占用，正在清理..."
        sudo lsof -t -i:5002 | xargs -r sudo kill -9
        # 等待进程完全结束
        sleep 2
    else
        echo "端口5002未被占用"
    fi
    
    # 构建并启动 Docker 服务
    echo "构建并启动 Docker 服务..."
    docker-compose up -d
    
    # 等待服务启动
    echo "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        echo "Docker 服务启动成功"
    else
        echo "Docker 服务启动失败"
        docker-compose logs
        exit 1
    fi
    
    # 保存 Docker 状态（如果需要）
    echo "保存 Docker 状态..."
    # Docker Compose 会自动管理容器状态
    
ENDSSH
check_status "Docker 部署完成" "Docker 部署失败"

echo_success "=== Docker 部署完成! ==="
echo_info "应用已通过 Docker 部署，可以通过域名 bbs.haoxueren.com 访问"
echo_info "如需查看日志，请在服务器上执行: docker-compose logs -f"
