# CSS样式管理规范

## 概述

本项目使用Tailwind CSS作为主要样式框架，为了避免样式冲突和优先级问题，我们建立了以下样式管理规范。

## 样式优先级问题解决方案

### 问题分析
项目之前存在以下样式冲突问题：
1. **内联样式** (style属性) 优先级最高，难以覆盖
2. **自定义CSS** 与 Tailwind CSS 类名冲突
3. **样式分散** 在多个文件中，维护困难
4. **重复定义** 同一效果用多种方式实现

### 解决方案

#### 1. 样式层级结构
```
优先级从高到低：
├── 内联样式 (style="...") - 【禁止使用】
├── 自定义CSS (!important) - 【特殊情况】
├── 自定义CSS 类 - 【Markdown样式等】
├── Tailwind 组件类 (@layer components) - 【推荐】
└── Tailwind 工具类 - 【推荐】
```

#### 2. 样式分类与使用规范

##### A. Tailwind 工具类 (推荐)
```html
<!-- 基础样式使用Tailwind工具类 -->
<div class="text-gray-500 text-sm text-right">
<button class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded">
```

##### B. 自定义组件类 (推荐)
```html
<!-- 复用性高的样式定义为组件类 -->
<div class="content-text">...</div>
<div class="content-meta">...</div>
<button class="dropdown-item-danger">删除</button>
```

##### C. 专用样式 (允许)
```html
<!-- Markdown渲染等特殊需求 -->
<div class="markdown-content">...</div>
```

##### D. 内联样式 (禁止)
```html
<!-- ❌ 禁止使用内联样式 -->
<div style="color: #dc2626;">...</div>

<!-- ✅ 使用类名替代 -->
<div class="text-red-600">...</div>
```

## 已优化的组件类

### 基础组件
- `.btn-primary` - 主要按钮
- `.btn-danger` - 危险按钮  
- `.btn-edit` - 编辑按钮
- `.card` - 卡片容器
- `.modal-overlay` - 模态遮罩
- `.modal-content` - 模态内容

### 菜单组件
- `.menu-btn` - 菜单按钮
- `.dropdown-menu` - 下拉菜单
- `.dropdown-item` - 菜单项
- `.dropdown-item-danger` - 危险菜单项

### 内容组件
- `.content-text` - 内容文本 (替代 font-size: 15px)
- `.content-meta` - 元信息 (替代 text-align: right)
- `.content-meta-index` - 索引页元信息

### 表单组件
- `.form-input` - 基础输入框
- `.form-input-datetime` - 日期时间输入
- `.form-input-category` - 分类输入
- `.textarea-reply` - 回复文本框

### 布局组件
- `.flex-between-center` - 两端对齐居中
- `.flex-center` - 居中对齐
- `.relative-container` - 相对定位容器
- `.absolute-bottom-right` - 绝对定位右下角

## 开发流程

### 1. 新增样式时
1. **优先使用** Tailwind 工具类
2. **需要复用时** 定义组件类到 `input.css`
3. **特殊需求时** 在对应组件的 `<style>` 标签中定义
4. **绝不使用** 内联样式

### 2. 修改样式时
1. **检查现有类** 是否满足需求
2. **修改组件类** 而不是添加新的内联样式
3. **测试影响范围** 确保不破坏其他组件

### 3. CSS构建流程
```bash
# 开发时监听模式
npm run build-css

# 生产构建
npm run build-css-prod

# 直接构建（一次性）
npx tailwindcss -i ./static/css/input.css -o ./static/css/output.css
```

## 样式文件结构

```
static/css/
├── input.css          # Tailwind CSS 源文件 (编辑这个)
└── output.css         # 编译后的CSS文件 (不要编辑)

templates/components/
└── post_styles.html   # 仅保留Markdown等特殊样式
```

## 常见问题与解决方案

### Q1: 样式不生效怎么办？
1. 检查是否构建了最新的CSS: `npx tailwindcss -i ./static/css/input.css -o ./static/css/output.css`
2. 清除浏览器缓存
3. 检查类名是否正确
4. 确认没有更高优先级的样式覆盖

### Q2: 如何调试样式冲突？
1. 使用浏览器开发者工具查看计算后的样式
2. 检查样式优先级和来源
3. 优先修改组件类而不是添加 `!important`

### Q3: 什么情况下可以使用自定义CSS？
1. Markdown渲染样式
2. 复杂的动画效果
3. 第三方库样式覆盖
4. 浏览器兼容性修复

## 最佳实践

1. **保持一致性**: 相同功能使用相同的类名
2. **语义化命名**: 类名应该表达用途而不是样式
3. **避免过度嵌套**: CSS选择器层级不超过3层
4. **文档化**: 新增组件类要添加注释说明
5. **定期清理**: 删除不使用的样式定义

## 迁移检查清单

- [x] 移除所有内联样式 (style属性)
- [x] 统一按钮样式为组件类
- [x] 统一内容文本样式
- [x] 统一布局相关样式
- [x] 清理重复的CSS定义
- [x] 构建新的CSS文件
- [ ] 测试各页面样式正常显示
- [ ] 验证响应式设计正常工作

---

遵循此规范可以有效避免样式冲突，提高代码维护性和开发效率。