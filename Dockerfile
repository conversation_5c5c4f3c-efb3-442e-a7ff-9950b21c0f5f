# 多阶段构建：构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装Node.js依赖
RUN npm ci

# 复制Tailwind配置和CSS源文件
COPY tailwind.config.js postcss.config.js ./
COPY static/css/input.css ./static/css/input.css

# 构建生产环境CSS
RUN npm run build-css-prod

# 多阶段构建：运行阶段
FROM python:3.9-alpine

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    sqlite \
    && rm -rf /var/cache/apk/*

# 复制Python依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 从构建阶段复制编译好的CSS
COPY --from=builder /app/static/css/output.css ./static/css/output.css

# 复制应用代码
COPY app.py ./
COPY static ./static
COPY templates ./templates

# 创建数据库文件（如果不存在）
RUN python -c "import sqlite3; conn = sqlite3.connect('forum.db'); conn.close()" || true

# 暴露端口
EXPOSE 5002

# 设置环境变量
ENV FLASK_ENV=production
ENV PORT=5002

# 启动命令
CMD ["python", "app.py"]
